<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准证件照App原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .screen {
            width: 320px;
            height: 568px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
            margin: 0 auto;
        }

        .screen-header {
            background: white;
            color: #333;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            background: none;
            border: none;
            color: #333;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
        }

        .screen-content {
            padding: 15px;
            height: calc(100% - 60px);
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .search-bar {
            background: #f0f0f0;
            border-radius: 20px;
            padding: 10px 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-bar input {
            border: none;
            background: none;
            outline: none;
            flex: 1;
            font-size: 14px;
        }

        .search-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 15px;
            font-size: 12px;
        }

        .photo-preview {
            width: 200px;
            height: 260px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-frame {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
        }

        .main-actions {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }

        .action-card {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-card:hover {
            transform: translateY(-2px);
        }

        .action-card .icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .action-card h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .action-card p {
            font-size: 12px;
            color: #666;
        }
        
        .camera-view {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 10px;
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .camera-guide {
            border: 2px dashed #4A90E2;
            width: 200px;
            height: 250px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4A90E2;
        }
        
        .capture-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: white;
            border: 4px solid #4A90E2;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .spec-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .spec-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .spec-card.active {
            border-color: #007AFF;
            background: #f0f8ff;
        }

        .spec-card.hot::after {
            content: "热门";
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff3b30;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .spec-card h3 {
            font-size: 15px;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .spec-card .size {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
        }

        .color-dots {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #ddd;
        }

        .color-dot.blue { background: #007AFF; }
        .color-dot.white { background: white; }
        .color-dot.red { background: #ff3b30; }
        .color-dot.navy { background: #1d3557; }
        .color-dot.gray { background: #8e8e93; }

        /* 证件照编辑页面样式 */
        .photo-editor {
            background: #f0f0f0;
            padding: 20px;
            text-align: center;
        }

        .photo-frame-container {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .photo-frame-outer {
            border: 2px solid #ff6b35;
            border-radius: 8px;
            padding: 10px;
            background: white;
            position: relative;
        }

        .photo-frame-inner {
            width: 200px;
            height: 260px;
            background: #007AFF;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .photo-dimensions {
            position: absolute;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .dimension-top {
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dimension-bottom {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dimension-left {
            left: -35px;
            top: 50%;
            transform: translateY(-50%) rotate(-90deg);
        }

        .dimension-right {
            right: -35px;
            top: 50%;
            transform: translateY(-50%) rotate(90deg);
        }

        .preview-badge {
            position: absolute;
            bottom: -5px;
            right: -5px;
            background: #ff6b35;
            color: white;
            padding: 4px 8px;
            border-radius: 0 0 8px 0;
            font-size: 10px;
            font-weight: 600;
        }

        .correction-icon {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007AFF;
            font-size: 16px;
        }

        .clothing-selector {
            background: white;
            border-radius: 15px 15px 0 0;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-height: 60%;
            overflow-y: auto;
        }

        .clothing-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }

        .clothing-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: none;
            border: none;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
        }

        .clothing-tab.active {
            color: #007AFF;
            border-bottom-color: #007AFF;
        }

        .clothing-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            padding: 15px;
        }

        .clothing-item {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .clothing-item.selected {
            border-color: #007AFF;
        }

        .clothing-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .clothing-placeholder {
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            text-align: center;
            padding: 5px;
        }

        .bottom-actions {
            display: flex;
            gap: 10px;
            padding: 20px;
            background: white;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .action-btn-outline {
            background: white;
            color: #007AFF;
            border: 2px solid #007AFF;
        }

        .action-btn-primary {
            background: #007AFF;
            color: white;
            border: none;
        }

        .edit-tools-bottom {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .edit-tool {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            position: relative;
        }

        .edit-tool.new::after {
            content: "新";
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff3b30;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 6px;
        }

        .edit-tool i {
            font-size: 24px;
        }

        .edit-tool span {
            font-size: 12px;
        }
        
        .edit-tools {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
        
        .tool-btn {
            background: none;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .tool-btn.active {
            background: #4A90E2;
            color: white;
        }
        
        .color-picker {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
        }
        
        .color-option.active {
            border-color: #333;
        }
        
        .preview-image {
            width: 200px;
            height: 250px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .history-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .history-thumb {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .settings-list {
            list-style: none;
        }
        
        .settings-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .screen-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 1. 主页 - 参考图片风格 -->
        <div class="screen">
            <div class="screen-content" style="padding: 0; background: white;">
                <!-- 证件照预览区域 -->
                <div class="photo-preview">
                    <div class="photo-frame"></div>
                    <div style="text-align: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.7;"></i>
                    </div>
                    <div style="position: absolute; top: 15px; right: 15px; background: rgba(255,255,255,0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-headphones" style="color: white; font-size: 16px;"></i>
                    </div>
                </div>

                <!-- 主要功能区 -->
                <div style="padding: 0 15px;">
                    <div class="main-actions">
                        <div class="action-card">
                            <div class="icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b35);">
                                <i class="fas fa-camera"></i>
                            </div>
                            <h3>拍摄证件照</h3>
                            <p>自助制作证件照，美颜、换装、冲印</p>
                        </div>
                    </div>

                    <div class="main-actions">
                        <div class="action-card">
                            <div class="icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                <i class="fas fa-user"></i>
                            </div>
                            <h3>我的记录</h3>
                            <p>冲印订单、电子证件照、原始照片</p>
                        </div>
                    </div>

                    <!-- 底部功能区 -->
                    <div style="display: flex; gap: 15px; margin-top: 20px;">
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #fa709a, #fee140); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-female"></i>
                            </div>
                            <h3 style="font-size: 14px;">专业形象照</h3>
                            <p>求职/商务/头像</p>
                        </div>
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #667eea, #764ba2); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-images"></i>
                            </div>
                            <h3 style="font-size: 14px;">证件照采集</h3>
                            <p>学校/企业/社区</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 规格列表页 - 参考图片风格 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">规格列表</h2>
                </div>
                <div class="header-right">
                    <i class="fas fa-ellipsis-h"></i>
                    <i class="fas fa-question-circle"></i>
                </div>
            </div>
            <div class="screen-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <i class="fas fa-search" style="color: #999;"></i>
                    <input type="text" placeholder="英语,计算机">
                    <button class="search-btn">搜索</button>
                </div>

                <!-- 规格网格 -->
                <div class="spec-grid">
                    <div class="spec-card active">
                        <h3>一寸</h3>
                        <div class="size">25x35mm | 295x413px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>二寸</h3>
                        <div class="size">35x49mm | 413x579px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>原图换底</h3>
                        <div class="size">不改尺寸，只换底色</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>导游证</h3>
                        <div class="size">23x33mm | 285x385px</div>
                        <div class="color-dots">
                            <div class="color-dot white"></div>
                        </div>
                    </div>
                </div>

                <!-- 广告区域 -->
                <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); border-radius: 12px; padding: 15px; margin: 20px 0; color: white; position: relative; overflow: hidden;">
                    <div style="position: absolute; right: -20px; top: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <h3 style="margin-bottom: 5px;">足不出户，坐等送货上门</h3>
                    <p style="font-size: 12px; opacity: 0.9;">小象超市</p>
                </div>

                <!-- 更多规格 -->
                <div class="spec-grid">
                    <div class="spec-card">
                        <h3>常规尺寸列表</h3>
                        <div class="size">各种常用尺寸的规格列表</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card hot">
                        <h3>自定义像素</h3>
                        <div class="size">自己设置照片的宽和高</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card hot">
                        <h3>专业形象照</h3>
                        <div class="size">求职/商务/头像</div>
                    </div>
                    <div class="spec-card">
                        <h3>全国计算机等级考试</h3>
                        <div class="size">33x48mm | 390x567px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3. 拍照指导页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">拍摄指导</h2>
                </div>
            </div>
            <div class="screen-content" style="background: white; text-align: center;">
                <!-- 示例照片 -->
                <div style="margin: 30px 0;">
                    <div style="width: 180px; height: 240px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                        <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 50px; opacity: 0.8;"></i>
                        </div>
                    </div>
                </div>

                <!-- 拍摄提示 -->
                <div style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin: 20px 0; text-align: left;">
                    <h3 style="margin-bottom: 15px; color: #333;">拍摄要求：</h3>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">正面免冠，表情自然</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">光线充足，背景简洁</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">着装整洁，符合要求</span>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div style="display: flex; gap: 15px; margin-top: 30px;">
                    <button style="flex: 1; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 10px; font-size: 16px;">
                        <i class="fas fa-images" style="margin-right: 8px;"></i>
                        从相册选择
                    </button>
                    <button style="flex: 1; padding: 15px; background: #007AFF; color: white; border: none; border-radius: 10px; font-size: 16px;">
                        <i class="fas fa-camera" style="margin-right: 8px;"></i>
                        立即拍摄
                    </button>
                </div>
            </div>
        </div>

        <!-- 4. 相机拍摄页 -->
        <div class="screen">
            <div style="background: black; height: 100%; position: relative;">
                <!-- 顶部控制栏 -->
                <div style="position: absolute; top: 0; left: 0; right: 0; z-index: 10; padding: 15px; display: flex; justify-content: space-between; align-items: center;">
                    <button class="back-btn" style="color: white;">
                        <i class="fas fa-times"></i>
                    </button>
                    <div style="color: white; font-size: 14px;">一寸照</div>
                    <button style="background: none; border: none; color: white; font-size: 18px;">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

                <!-- 相机预览区 -->
                <div style="position: absolute; top: 60px; left: 0; right: 0; bottom: 120px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 200px; height: 260px; border: 2px dashed rgba(255,255,255,0.5); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: rgba(255,255,255,0.7);">
                        <div style="text-align: center;">
                            <i class="fas fa-user" style="font-size: 60px; margin-bottom: 10px;"></i>
                            <div style="font-size: 12px;">请将面部对准框内</div>
                        </div>
                    </div>
                </div>

                <!-- 底部控制栏 -->
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 30px; display: flex; justify-content: space-between; align-items: center;">
                    <button style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border: none; border-radius: 50%; color: white; font-size: 20px;">
                        <i class="fas fa-images"></i>
                    </button>
                    <button style="width: 80px; height: 80px; background: white; border: 4px solid rgba(255,255,255,0.3); border-radius: 50%; color: #007AFF; font-size: 30px;">
                        <i class="fas fa-camera"></i>
                    </button>
                    <button style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border: none; border-radius: 50%; color: white; font-size: 20px;">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 5. 证件照编辑页 - 一寸照 (参考图片1) -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">一寸</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #333; font-size: 18px;">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <button style="background: none; border: none; color: #333; font-size: 18px; margin-left: 10px;">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片编辑区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner">
                                <!-- 实际照片内容 -->
                                <div style="width: 100%; height: 100%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 200 260\"><rect width=\"200\" height=\"260\" fill=\"%23007AFF\"/><circle cx=\"100\" cy=\"80\" r=\"25\" fill=\"%23f4c2a1\"/><rect x=\"85\" y=\"105\" width=\"30\" height=\"40\" fill=\"%23333\" rx=\"15\"/><rect x=\"75\" y=\"145\" width=\"50\" height=\"115\" fill=\"%23333\"/></svg>') center/cover; position: relative;">
                                    <!-- 预览图标 -->
                                    <div class="correction-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">295px</div>
                            <div class="photo-dimensions dimension-bottom">25mm</div>
                            <div class="photo-dimensions dimension-left">413px</div>
                            <div class="photo-dimensions dimension-right">35mm</div>
                        </div>
                        <!-- 预览标签 -->
                        <div class="preview-badge">预览图</div>
                        <!-- 正装标识 -->
                        <div style="position: absolute; bottom: 10px; right: 60px; background: rgba(255,255,255,0.9); border-radius: 15px; padding: 5px 10px; font-size: 12px; color: #007AFF;">
                            <i class="fas fa-user-tie" style="margin-right: 5px;"></i>
                            正装
                        </div>
                    </div>

                    <!-- 广告区域 -->
                    <div style="background: white; border-radius: 8px; margin: 20px; padding: 10px; display: flex; align-items: center; gap: 10px;">
                        <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 40'><rect width='60' height='40' fill='%23333'/><text x='30' y='25' text-anchor='middle' fill='white' font-size='8'>广告</text></svg>" style="width: 60px; height: 40px; border-radius: 4px;">
                        <div style="flex: 1;">
                            <div style="font-size: 13px; color: #333; font-weight: 600;">奥迪Q5L "拍了拍你，</div>
                            <div style="font-size: 13px; color: #333;">购车可享5年0首付政</div>
                            <div style="font-size: 11px; color: #666; margin-top: 5px;">
                                <span style="background: #f0f0f0; padding: 2px 6px; border-radius: 10px;">奥迪</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 换装选择器 -->
                <div class="clothing-selector">
                    <div class="clothing-tabs">
                        <button class="clothing-tab active">女装</button>
                        <button class="clothing-tab">男装</button>
                        <button class="clothing-tab">童装</button>
                        <button style="background: none; border: none; padding: 15px; color: #666;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="clothing-grid">
                        <div class="clothing-item selected">
                            <div class="clothing-placeholder">
                                <i class="fas fa-user" style="font-size: 20px;"></i>
                                <br>无正装
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                lady007
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                lady001
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                lady008
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                lady003
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                lady010
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 6. 证件照编辑页 - 二寸照 (参考图片2) -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">二寸</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #333; font-size: 18px;">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <button style="background: none; border: none; color: #333; font-size: 18px; margin-left: 10px;">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片编辑区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner" style="width: 220px; height: 300px;">
                                <!-- 实际照片内容 -->
                                <div style="width: 100%; height: 100%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 220 300\"><rect width=\"220\" height=\"300\" fill=\"%23007AFF\"/><circle cx=\"110\" cy=\"90\" r=\"30\" fill=\"%23f4c2a1\"/><rect x=\"90\" y=\"120\" width=\"40\" height=\"50\" fill=\"%23333\" rx=\"20\"/><rect x=\"80\" y=\"170\" width=\"60\" height=\"130\" fill=\"%23333\"/></svg>') center/cover; position: relative;">
                                    <!-- 预览图标 -->
                                    <div class="correction-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">413px</div>
                            <div class="photo-dimensions dimension-bottom">35mm</div>
                            <div class="photo-dimensions dimension-left">579px</div>
                            <div class="photo-dimensions dimension-right">49mm</div>
                        </div>
                        <!-- 预览标签 -->
                        <div class="preview-badge">预览图</div>
                        <!-- 正装标识 -->
                        <div style="position: absolute; bottom: 10px; right: 60px; background: rgba(255,255,255,0.9); border-radius: 15px; padding: 5px 10px; font-size: 12px; color: #007AFF;">
                            <i class="fas fa-user-tie" style="margin-right: 5px;"></i>
                            正装
                        </div>
                    </div>

                    <!-- 广告区域 -->
                    <div style="background: white; border-radius: 8px; margin: 20px; padding: 10px; display: flex; align-items: center; gap: 10px;">
                        <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 40'><rect width='60' height='40' fill='%234a90e2'/><text x='30' y='25' text-anchor='middle' fill='white' font-size='6'>游戏广告</text></svg>" style="width: 60px; height: 40px; border-radius: 4px;">
                        <div style="flex: 1;">
                            <div style="font-size: 13px; color: #333; font-weight: 600;">激情三国，智谋对战，</div>
                            <div style="font-size: 13px; color: #333;">阵容搭配，我在三国等</div>
                            <div style="font-size: 11px; color: #666; margin-top: 5px;">
                                <span style="background: #f0f0f0; padding: 2px 6px; border-radius: 10px;">三国志临天下</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部编辑工具 -->
                <div class="edit-tools-bottom">
                    <button class="edit-tool">
                        <i class="fas fa-palette"></i>
                        <span>换底色</span>
                    </button>
                    <button class="edit-tool">
                        <i class="fas fa-font"></i>
                        <span>加文字</span>
                    </button>
                    <button class="edit-tool new">
                        <i class="fas fa-tshirt"></i>
                        <span>换服装</span>
                    </button>
                </div>

                <!-- 底部操作按钮 -->
                <div class="bottom-actions">
                    <button class="action-btn action-btn-outline">
                        <i class="fas fa-download"></i>
                        下载电子照
                    </button>
                    <button class="action-btn action-btn-primary">
                        <i class="fas fa-print"></i>
                        冲印排版照
                    </button>
                </div>
            </div>
        </div>

        <!-- 7. 换底色页面 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">换底色</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">完成</button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片预览区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner">
                                <div style="width: 100%; height: 100%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 200 260\"><rect width=\"200\" height=\"260\" fill=\"%23007AFF\"/><circle cx=\"100\" cy=\"80\" r=\"25\" fill=\"%23f4c2a1\"/><rect x=\"85\" y=\"105\" width=\"30\" height=\"40\" fill=\"%23333\" rx=\"15\"/><rect x=\"75\" y=\"145\" width=\"50\" height=\"115\" fill=\"%23333\"/></svg>') center/cover; position: relative;">
                                    <!-- AI处理指示器 -->
                                    <div style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 10px;">
                                        <i class="fas fa-magic" style="margin-right: 4px;"></i>
                                        AI处理中...
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">295px</div>
                            <div class="photo-dimensions dimension-bottom">25mm</div>
                            <div class="photo-dimensions dimension-left">413px</div>
                            <div class="photo-dimensions dimension-right">35mm</div>
                        </div>
                    </div>
                </div>

                <!-- 颜色选择区域 -->
                <div style="background: white; padding: 20px;">
                    <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">选择背景色</div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #007AFF; border-radius: 50%; margin: 0 auto 8px; border: 3px solid #007AFF; box-shadow: 0 2px 10px rgba(0,122,255,0.3); position: relative;">
                                <i class="fas fa-check" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 16px;"></i>
                            </div>
                            <div style="font-size: 12px; color: #333; font-weight: 600;">蓝色</div>
                            <div style="font-size: 10px; color: #666;">#007AFF</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: white; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">白色</div>
                            <div style="font-size: 10px; color: #666;">#FFFFFF</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #ff3b30; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">红色</div>
                            <div style="font-size: 10px; color: #666;">#FF3B30</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #1d3557; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">深蓝</div>
                            <div style="font-size: 10px; color: #666;">#1D3557</div>
                        </div>
                    </div>

                    <!-- 更多颜色 -->
                    <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 10px; margin-bottom: 20px;">
                        <div style="width: 40px; height: 40px; background: #34c759; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #ff9500; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #af52de; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #ff2d92; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #8e8e93; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b35, #f7931e); border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                    </div>

                    <!-- 自定义颜色 -->
                    <button style="width: 100%; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 10px; font-size: 14px; margin-bottom: 15px;">
                        <i class="fas fa-palette" style="margin-right: 8px;"></i>
                        自定义颜色
                    </button>

                    <!-- AI智能抠图说明 -->
                    <div style="background: #e3f2fd; border-radius: 10px; padding: 15px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <i class="fas fa-magic" style="color: #007AFF; margin-right: 8px;"></i>
                            <span style="font-weight: 600; color: #333; font-size: 14px;">AI智能抠图</span>
                        </div>
                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                            • 自动识别人像轮廓，精确去除背景<br>
                            • 保留头发丝等细节，抠图更自然<br>
                            • 支持一键更换任意背景颜色
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 8. 美颜编辑页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">美颜调整</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">完成</button>
                </div>
            </div>
            <div class="screen-content" style="background: white; padding: 0;">
                <!-- 照片预览 -->
                <div style="padding: 20px; text-align: center; background: #f8f9fa;">
                    <div style="width: 200px; height: 260px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.8;"></i>
                    </div>
                </div>

                <!-- 美颜调节项 -->
                <div style="padding: 20px;">
                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">磨皮</span>
                            <span style="font-size: 13px; color: #007AFF;">60</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 60%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 40%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">美白</span>
                            <span style="font-size: 13px; color: #007AFF;">40</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 40%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 60%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">瘦脸</span>
                            <span style="font-size: 13px; color: #007AFF;">30</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 30%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 70%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">大眼</span>
                            <span style="font-size: 13px; color: #007AFF;">20</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 20%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 80%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <!-- 预设方案 -->
                    <div style="margin-top: 30px;">
                        <div style="font-size: 15px; color: #333; margin-bottom: 15px;">预设方案</div>
                        <div style="display: flex; gap: 10px;">
                            <button style="flex: 1; padding: 10px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 13px;">自然</button>
                            <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 13px;">清新</button>
                            <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 13px;">质感</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 9. 完成预览页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">预览效果</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: white; text-align: center;">
                <!-- 最终效果展示 -->
                <div style="padding: 30px 20px;">
                    <div style="width: 200px; height: 260px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 30px rgba(0,0,0,0.15); background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.8;"></i>
                    </div>
                    <div style="margin-top: 20px;">
                        <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 5px;">一寸证件照</div>
                        <div style="font-size: 14px; color: #666;">25×35mm | 蓝色背景</div>
                    </div>
                </div>

                <!-- 操作按钮组 -->
                <div style="padding: 0 20px;">
                    <button style="width: 100%; padding: 15px; background: #007AFF; color: white; border: none; border-radius: 12px; font-size: 16px; font-weight: 600; margin-bottom: 15px;">
                        <i class="fas fa-download" style="margin-right: 8px;"></i>
                        保存到相册
                    </button>

                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button style="flex: 1; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 10px; font-size: 14px;">
                            <i class="fas fa-print" style="margin-right: 5px;"></i>
                            冲印
                        </button>
                        <button style="flex: 1; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 10px; font-size: 14px;">
                            <i class="fas fa-share" style="margin-right: 5px;"></i>
                            分享
                        </button>
                    </div>

                    <button style="width: 100%; padding: 12px; background: none; color: #666; border: 1px solid #ddd; border-radius: 10px; font-size: 14px;">
                        <i class="fas fa-edit" style="margin-right: 5px;"></i>
                        重新编辑
                    </button>
                </div>

                <!-- 规格信息 -->
                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 12px; text-align: left;">
                    <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 10px;">照片信息</div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">尺寸规格</span>
                        <span style="color: #333; font-size: 13px;">25×35mm (一寸)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">像素大小</span>
                        <span style="color: #333; font-size: 13px;">295×413px</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">背景颜色</span>
                        <span style="color: #333; font-size: 13px;">蓝色 (#007AFF)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span style="color: #666; font-size: 13px;">文件大小</span>
                        <span style="color: #333; font-size: 13px;">156KB</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 10. 我的记录页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">我的记录</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">管理</button>
                </div>
            </div>
            <div class="screen-content">
                <!-- 统计卡片 -->
                <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #007AFF; margin-bottom: 5px;">12</div>
                        <div style="font-size: 12px; color: #666;">制作次数</div>
                    </div>
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #28a745; margin-bottom: 5px;">8</div>
                        <div style="font-size: 12px; color: #666;">冲印订单</div>
                    </div>
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #ff6b35; margin-bottom: 5px;">24</div>
                        <div style="font-size: 12px; color: #666;">保存照片</div>
                    </div>
                </div>

                <!-- 记录列表 -->
                <div style="background: white; border-radius: 12px; overflow: hidden;">
                    <div style="padding: 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">一寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">25×35mm | 蓝色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-15 14:30</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 5px;">
                            <button style="padding: 4px 8px; background: #007AFF; color: white; border: none; border-radius: 4px; font-size: 10px;">下载</button>
                            <button style="padding: 4px 8px; background: #f8f9fa; color: #666; border: 1px solid #ddd; border-radius: 4px; font-size: 10px;">分享</button>
                        </div>
                    </div>

                    <div style="padding: 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">二寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">35×49mm | 红色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-12 09:15</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 5px;">
                            <button style="padding: 4px 8px; background: #007AFF; color: white; border: none; border-radius: 4px; font-size: 10px;">下载</button>
                            <button style="padding: 4px 8px; background: #f8f9fa; color: #666; border: 1px solid #ddd; border-radius: 4px; font-size: 10px;">分享</button>
                        </div>
                    </div>

                    <div style="padding: 15px; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">小二寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">35×45mm | 白色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-08 16:45</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 5px;">
                            <button style="padding: 4px 8px; background: #007AFF; color: white; border: none; border-radius: 4px; font-size: 10px;">下载</button>
                            <button style="padding: 4px 8px; background: #f8f9fa; color: #666; border: 1px solid #ddd; border-radius: 4px; font-size: 10px;">分享</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 11. 专业形象照页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">专业形象照</h2>
                </div>
            </div>
            <div class="screen-content">
                <!-- 类型选择 -->
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button style="flex: 1; padding: 10px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 14px;">求职照</button>
                        <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">商务照</button>
                        <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">头像照</button>
                    </div>
                </div>

                <!-- 模板展示 -->
                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div style="width: 80px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user-tie" style="font-size: 30px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin-bottom: 8px; font-size: 16px;">商务正装</h3>
                            <p style="font-size: 13px; color: #666; line-height: 1.4; margin-bottom: 10px;">适合求职面试、职场商务等正式场合使用</p>
                            <div style="display: flex; gap: 5px;">
                                <span style="background: #e3f2fd; color: #007AFF; padding: 2px 8px; border-radius: 12px; font-size: 11px;">正装</span>
                                <span style="background: #e8f5e8; color: #28a745; padding: 2px 8px; border-radius: 12px; font-size: 11px;">专业</span>
                            </div>
                        </div>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 14px;">
                        选择此模板
                    </button>
                </div>

                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div style="width: 80px; height: 100px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 30px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin-bottom: 8px; font-size: 16px;">休闲商务</h3>
                            <p style="font-size: 13px; color: #666; line-height: 1.4; margin-bottom: 10px;">适合创意行业、互联网公司等轻松环境</p>
                            <div style="display: flex; gap: 5px;">
                                <span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 11px;">休闲</span>
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 11px;">时尚</span>
                            </div>
                        </div>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                        选择此模板
                    </button>
                </div>

                <!-- 拍摄提示 -->
                <div style="background: #e3f2fd; border-radius: 10px; padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-lightbulb" style="color: #007AFF; margin-right: 10px;"></i>
                        <span style="font-weight: 600; color: #333;">拍摄建议</span>
                    </div>
                    <ul style="font-size: 13px; color: #666; line-height: 1.5; margin-left: 20px;">
                        <li>选择合适的服装搭配</li>
                        <li>保持自然的表情和姿态</li>
                        <li>注意光线和背景环境</li>
                        <li>可适当进行美颜处理</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
