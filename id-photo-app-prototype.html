<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准证件照App原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .screen {
            width: 320px;
            height: 568px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            position: relative;
            margin: 0 auto;
        }
        
        .screen-header {
            background: #4A90E2;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .screen-content {
            padding: 20px;
            height: calc(100% - 60px);
            overflow-y: auto;
        }
        
        .logo {
            text-align: center;
            margin: 60px 0 40px;
        }
        
        .logo i {
            font-size: 80px;
            color: #4A90E2;
            margin-bottom: 20px;
        }
        
        .logo h1 {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #666;
            font-size: 16px;
        }
        
        .main-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 40px;
        }
        
        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #4A90E2;
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .camera-view {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 10px;
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .camera-guide {
            border: 2px dashed #4A90E2;
            width: 200px;
            height: 250px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4A90E2;
        }
        
        .capture-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: white;
            border: 4px solid #4A90E2;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .spec-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .spec-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .spec-card.active {
            border-color: #4A90E2;
            background: #e3f2fd;
        }
        
        .spec-card h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .spec-card p {
            font-size: 12px;
            color: #666;
        }
        
        .edit-tools {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
        
        .tool-btn {
            background: none;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .tool-btn.active {
            background: #4A90E2;
            color: white;
        }
        
        .color-picker {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
        }
        
        .color-option.active {
            border-color: #333;
        }
        
        .preview-image {
            width: 200px;
            height: 250px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .history-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .history-thumb {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .settings-list {
            list-style: none;
        }
        
        .settings-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .screen-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 1. 启动页 -->
        <div class="screen">
            <div class="screen-content">
                <div class="logo">
                    <i class="fas fa-camera"></i>
                    <h1>证件照大师</h1>
                    <p>专业证件照制作工具</p>
                </div>
            </div>
        </div>

        <!-- 2. 主页 -->
        <div class="screen">
            <div class="screen-header">
                <h2>证件照大师</h2>
            </div>
            <div class="screen-content">
                <div class="main-buttons">
                    <button class="btn btn-primary">
                        <i class="fas fa-camera"></i>
                        拍摄证件照
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-images"></i>
                        从相册选择
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-history"></i>
                        历史记录
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 3. 拍照页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>拍摄证件照</h2>
            </div>
            <div class="screen-content">
                <div class="tips">
                    <i class="fas fa-info-circle"></i>
                    请将面部对准虚线框内，保持正面拍摄
                </div>
                <div class="camera-view">
                    <div class="camera-guide">
                        <i class="fas fa-user" style="font-size: 60px;"></i>
                    </div>
                </div>
                <div class="capture-btn">
                    <i class="fas fa-camera" style="font-size: 24px; color: #4A90E2;"></i>
                </div>
            </div>
        </div>

        <!-- 4. 规格选择页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>选择证件照规格</h2>
            </div>
            <div class="screen-content">
                <div class="spec-grid">
                    <div class="spec-card active">
                        <h3>一寸照</h3>
                        <p>25mm × 35mm</p>
                    </div>
                    <div class="spec-card">
                        <h3>二寸照</h3>
                        <p>35mm × 49mm</p>
                    </div>
                    <div class="spec-card">
                        <h3>小二寸</h3>
                        <p>35mm × 45mm</p>
                    </div>
                    <div class="spec-card">
                        <h3>大二寸</h3>
                        <p>35mm × 53mm</p>
                    </div>
                </div>
                <button class="btn btn-primary" style="margin-top: 20px;">
                    下一步
                </button>
            </div>
        </div>

        <!-- 5. 编辑页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>编辑证件照</h2>
            </div>
            <div class="screen-content">
                <div class="preview-image">
                    <i class="fas fa-user" style="font-size: 60px;"></i>
                </div>
                <div class="edit-tools">
                    <button class="tool-btn active">
                        <i class="fas fa-palette"></i>
                        背景
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-crop"></i>
                        裁剪
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-magic"></i>
                        美颜
                    </button>
                </div>
                <div class="color-picker">
                    <div class="color-option active" style="background: #ff4757;"></div>
                    <div class="color-option" style="background: #3742fa;"></div>
                    <div class="color-option" style="background: #ffffff; border: 1px solid #ddd;"></div>
                    <div class="color-option" style="background: #2f3542;"></div>
                </div>
                <button class="btn btn-primary">
                    完成编辑
                </button>
            </div>
        </div>

        <!-- 6. 预览页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>预览效果</h2>
            </div>
            <div class="screen-content">
                <div class="screen-title">一寸照 (25mm × 35mm)</div>
                <div class="preview-image" style="margin-bottom: 30px;">
                    <i class="fas fa-user" style="font-size: 60px;"></i>
                </div>
                <div class="main-buttons">
                    <button class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存到相册
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-share"></i>
                        分享
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-print"></i>
                        打印排版
                    </button>
                </div>
            </div>
        </div>

        <!-- 7. 保存分享页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>保存选项</h2>
            </div>
            <div class="screen-content">
                <div class="preview-image" style="margin-bottom: 20px;">
                    <i class="fas fa-user" style="font-size: 60px;"></i>
                </div>
                <div class="tips">
                    <i class="fas fa-check-circle"></i>
                    证件照已成功保存到相册
                </div>
                <div class="main-buttons">
                    <button class="btn btn-primary">
                        <i class="fas fa-wechat"></i>
                        分享到微信
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-qq"></i>
                        分享到QQ
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-copy"></i>
                        复制链接
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        返回首页
                    </button>
                </div>
            </div>
        </div>

        <!-- 8. 历史记录页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>历史记录</h2>
            </div>
            <div class="screen-content">
                <div class="history-grid">
                    <div class="history-item">
                        <div class="history-thumb">
                            <i class="fas fa-user"></i>
                        </div>
                        <p style="font-size: 12px; color: #666;">一寸照</p>
                        <p style="font-size: 10px; color: #999;">2024-01-15</p>
                    </div>
                    <div class="history-item">
                        <div class="history-thumb">
                            <i class="fas fa-user"></i>
                        </div>
                        <p style="font-size: 12px; color: #666;">二寸照</p>
                        <p style="font-size: 10px; color: #999;">2024-01-10</p>
                    </div>
                    <div class="history-item">
                        <div class="history-thumb">
                            <i class="fas fa-user"></i>
                        </div>
                        <p style="font-size: 12px; color: #666;">小二寸</p>
                        <p style="font-size: 10px; color: #999;">2024-01-08</p>
                    </div>
                    <div class="history-item">
                        <div class="history-thumb">
                            <i class="fas fa-user"></i>
                        </div>
                        <p style="font-size: 12px; color: #666;">大二寸</p>
                        <p style="font-size: 10px; color: #999;">2024-01-05</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 9. 设置页 -->
        <div class="screen">
            <div class="screen-header">
                <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                <h2>设置</h2>
            </div>
            <div class="screen-content">
                <ul class="settings-list">
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-image" style="margin-right: 10px; color: #4A90E2;"></i>
                            图片质量
                        </div>
                        <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                    </li>
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-palette" style="margin-right: 10px; color: #4A90E2;"></i>
                            默认背景色
                        </div>
                        <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                    </li>
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-magic" style="margin-right: 10px; color: #4A90E2;"></i>
                            自动美颜
                        </div>
                        <div style="width: 40px; height: 20px; background: #4A90E2; border-radius: 10px; position: relative;">
                            <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; right: 1px; top: 1px;"></div>
                        </div>
                    </li>
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-bell" style="margin-right: 10px; color: #4A90E2;"></i>
                            消息通知
                        </div>
                        <div style="width: 40px; height: 20px; background: #ddd; border-radius: 10px; position: relative;">
                            <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; left: 1px; top: 1px;"></div>
                        </div>
                    </li>
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-question-circle" style="margin-right: 10px; color: #4A90E2;"></i>
                            帮助与反馈
                        </div>
                        <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                    </li>
                    <li class="settings-item">
                        <div>
                            <i class="fas fa-info-circle" style="margin-right: 10px; color: #4A90E2;"></i>
                            关于我们
                        </div>
                        <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
