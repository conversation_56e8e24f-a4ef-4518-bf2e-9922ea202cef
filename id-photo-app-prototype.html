<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准证件照App原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* ===== 设计系统 - Design System ===== */
        :root {
            /* 主色调 */
            --primary-color: #007AFF;
            --primary-light: #4DA3FF;
            --primary-dark: #0056CC;
            --primary-bg: rgba(0, 122, 255, 0.1);

            /* 辅助色 */
            --secondary-color: #34C759;
            --accent-color: #FF3B30;
            --warning-color: #FF9500;
            --info-color: #5AC8FA;

            /* 中性色 */
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --text-tertiary: #C7C7CC;
            --text-white: #FFFFFF;

            /* 背景色 */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F2F2F7;
            --bg-tertiary: #F8F9FA;
            --bg-overlay: rgba(0, 0, 0, 0.4);

            /* 边框色 */
            --border-light: #E5E5EA;
            --border-medium: #D1D1D6;
            --border-dark: #C7C7CC;

            /* 圆角 */
            --radius-xs: 4px;
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
            --radius-full: 50%;

            /* 间距 */
            --space-xs: 4px;
            --space-sm: 8px;
            --space-md: 12px;
            --space-lg: 16px;
            --space-xl: 20px;
            --space-2xl: 24px;
            --space-3xl: 32px;

            /* 阴影 */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --shadow-xl: 0 16px 40px rgba(0, 0, 0, 0.15);

            /* 字体 */
            --font-xs: 10px;
            --font-sm: 12px;
            --font-md: 14px;
            --font-lg: 16px;
            --font-xl: 18px;
            --font-2xl: 20px;
            --font-3xl: 24px;
            --font-4xl: 28px;

            /* 字重 */
            --font-regular: 400;
            --font-medium: 500;
            --font-semibold: 600;
            --font-bold: 700;

            /* 过渡 */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.25s ease;
            --transition-slow: 0.35s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: var(--font-md);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* ===== 基础布局 ===== */
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: var(--space-3xl);
            padding: var(--space-3xl);
            max-width: 1600px;
            margin: 0 auto;
        }

        .screen {
            width: 375px;
            height: 812px;
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            position: relative;
            margin: 0 auto;
        }

        /* ===== 统一头部导航 ===== */
        .screen-header {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: var(--space-lg) var(--space-xl);
            min-height: 64px;
            font-weight: var(--font-semibold);
            position: relative;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: var(--font-xl);
            cursor: pointer;
            padding: var(--space-sm);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: var(--primary-bg);
        }

        /* ===== 统一按钮系统 ===== */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-md) var(--space-xl);
            border: none;
            border-radius: var(--radius-md);
            font-size: var(--font-md);
            font-weight: var(--font-medium);
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-normal);
            min-height: 44px;
            gap: var(--space-sm);
            font-family: inherit;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-light);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--border-medium);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-bg);
        }

        .btn-text {
            background: transparent;
            color: var(--primary-color);
            padding: var(--space-sm) var(--space-md);
            min-height: auto;
        }

        .btn-text:hover {
            background: var(--primary-bg);
        }

        .btn-sm {
            padding: var(--space-sm) var(--space-md);
            font-size: var(--font-sm);
            min-height: 36px;
        }

        .btn-lg {
            padding: var(--space-lg) var(--space-2xl);
            font-size: var(--font-lg);
            min-height: 52px;
        }

        .btn-full {
            width: 100%;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* ===== 工具卡片样式 ===== */
        .tool-card {
            border-radius: var(--radius-lg);
            padding: var(--space-xl);
            margin-bottom: var(--space-lg);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .tool-card .tool-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-lg);
            color: var(--text-white);
            font-size: var(--font-xl);
        }

        .tool-card .tool-title {
            font-size: var(--font-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-xs);
        }

        .tool-card .tool-desc {
            font-size: var(--font-sm);
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .tool-card .tool-btn {
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--radius-xl);
            font-size: var(--font-sm);
            font-weight: var(--font-semibold);
            border: none;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        /* ===== 菜单项悬停效果 ===== */
        .menu-item {
            transition: all var(--transition-fast);
        }

        .menu-item:hover {
            background: var(--bg-tertiary);
        }

        /* ===== 统一内容区域 ===== */
        .screen-content {
            padding: var(--space-xl);
            height: calc(100% - 64px);
            overflow-y: auto;
            background: var(--bg-secondary);
        }

        /* ===== 统一卡片系统 ===== */
        .card {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: all var(--transition-normal);
            border: 1px solid var(--border-light);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-body {
            padding: var(--space-xl);
        }

        .card-header {
            padding: var(--space-lg) var(--space-xl);
            border-bottom: 1px solid var(--border-light);
            background: var(--bg-tertiary);
            font-weight: var(--font-semibold);
        }

        .card-footer {
            padding: var(--space-lg) var(--space-xl);
            border-top: 1px solid var(--border-light);
            background: var(--bg-tertiary);
        }

        /* ===== 统一表单元素 ===== */
        .form-input {
            width: 100%;
            padding: var(--space-md) var(--space-lg);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-md);
            font-size: var(--font-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-bg);
        }

        .form-input::placeholder {
            color: var(--text-tertiary);
        }
        
        /* ===== 搜索栏样式 ===== */
        .search-bar {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            padding: var(--space-md) var(--space-lg);
            margin-bottom: var(--space-xl);
            display: flex;
            align-items: center;
            gap: var(--space-md);
            box-shadow: var(--shadow-sm);
        }

        .search-bar input {
            border: none;
            background: none;
            outline: none;
            flex: 1;
            font-size: var(--font-md);
            color: var(--text-primary);
        }

        .search-bar input::placeholder {
            color: var(--text-tertiary);
        }

        .search-btn {
            background: var(--primary-color);
            color: var(--text-white);
            border: none;
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--radius-lg);
            font-size: var(--font-sm);
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .search-btn:hover {
            background: var(--primary-dark);
        }

        .photo-preview {
            width: 200px;
            height: 260px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-frame {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
        }

        /* ===== 主页面功能卡片 ===== */
        .main-actions {
            display: flex;
            gap: var(--space-lg);
            margin: var(--space-xl) 0;
        }

        .action-card {
            flex: 1;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-xl);
            text-align: center;
            box-shadow: var(--shadow-md);
            cursor: pointer;
            transition: all var(--transition-normal);
            border: 1px solid var(--border-light);
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .action-card .icon {
            width: 56px;
            height: 56px;
            border-radius: var(--radius-md);
            margin: 0 auto var(--space-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-3xl);
            color: var(--text-white);
            box-shadow: var(--shadow-sm);
        }

        .action-card h3 {
            font-size: var(--font-lg);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-sm);
            color: var(--text-primary);
        }

        .action-card p {
            font-size: var(--font-sm);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .camera-view {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 10px;
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .camera-guide {
            border: 2px dashed #4A90E2;
            width: 200px;
            height: 250px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4A90E2;
        }
        
        .capture-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: white;
            border: 4px solid #4A90E2;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        /* ===== 规格卡片样式 ===== */
        .spec-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-md);
            margin-bottom: var(--space-xl);
        }

        .spec-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            box-shadow: var(--shadow-sm);
        }

        .spec-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .spec-card.active {
            border-color: var(--primary-color);
            background: var(--primary-bg);
            box-shadow: var(--shadow-md);
        }

        .spec-card.hot::after {
            content: "热门";
            position: absolute;
            top: -6px;
            right: -6px;
            background: var(--accent-color);
            color: var(--text-white);
            font-size: var(--font-xs);
            font-weight: var(--font-semibold);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            box-shadow: var(--shadow-sm);
        }

        .spec-card h3 {
            font-size: var(--font-lg);
            margin-bottom: var(--space-sm);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
        }

        .spec-card .size {
            font-size: var(--font-sm);
            color: var(--text-secondary);
            margin-bottom: var(--space-md);
            line-height: 1.4;
        }

        .color-dots {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #ddd;
        }

        .color-dot.blue { background: #007AFF; }
        .color-dot.white { background: white; }
        .color-dot.red { background: #ff3b30; }
        .color-dot.navy { background: #1d3557; }
        .color-dot.gray { background: #8e8e93; }

        /* 证件照编辑页面样式 */
        .photo-editor {
            background: #f0f0f0;
            padding: 20px;
            text-align: center;
        }

        .photo-frame-container {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .photo-frame-outer {
            border: 2px solid #ff6b35;
            border-radius: 8px;
            padding: 10px;
            background: white;
            position: relative;
        }

        .photo-frame-inner {
            width: 200px;
            height: 260px;
            background: #007AFF;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .photo-dimensions {
            position: absolute;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .dimension-top {
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dimension-bottom {
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dimension-left {
            left: -30px;
            top: 50%;
            transform: translateY(-50%) rotate(-90deg);
            transform-origin: center;
        }

        .dimension-right {
            right: -30px;
            top: 50%;
            transform: translateY(-50%) rotate(90deg);
            transform-origin: center;
        }

        .preview-badge {
            position: absolute;
            bottom: -5px;
            right: -5px;
            background: #ff6b35;
            color: white;
            padding: 4px 8px;
            border-radius: 0 0 8px 0;
            font-size: 10px;
            font-weight: 600;
        }

        .correction-icon {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007AFF;
            font-size: 16px;
        }

        .clothing-selector {
            background: white;
            border-radius: 15px 15px 0 0;
            margin-top: 20px;
            overflow: hidden;
        }

        .clothing-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background: white;
        }

        .clothing-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: none;
            border: none;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
        }

        .clothing-tab.active {
            color: #007AFF;
            border-bottom-color: #007AFF;
        }

        .clothing-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            padding: 15px;
        }

        .clothing-item {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .clothing-item.selected {
            border-color: #007AFF;
        }

        .clothing-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .clothing-placeholder {
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            text-align: center;
            padding: 5px;
        }

        .bottom-actions {
            display: flex;
            gap: 10px;
            padding: 20px;
            background: white;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .action-btn-outline {
            background: white;
            color: #007AFF;
            border: 2px solid #007AFF;
        }

        .action-btn-primary {
            background: #007AFF;
            color: white;
            border: none;
        }

        .edit-tools-bottom {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .edit-tool {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            position: relative;
        }

        .edit-tool.new::after {
            content: "新";
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff3b30;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 6px;
        }

        .edit-tool i {
            font-size: 24px;
        }

        .edit-tool span {
            font-size: 12px;
        }
        
        .edit-tools {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
        
        .tool-btn {
            background: none;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .tool-btn.active {
            background: #4A90E2;
            color: white;
        }
        
        .color-picker {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
        }
        
        .color-option.active {
            border-color: #333;
        }
        
        .preview-image {
            width: 200px;
            height: 250px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .history-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .history-thumb {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .settings-list {
            list-style: none;
        }
        
        .settings-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .screen-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 1. 主页 - 参考图片风格 -->
        <div class="screen">
            <div class="screen-content" style="padding: 0; background: white;">
                <!-- 证件照预览区域 -->
                <div class="photo-preview">
                    <div class="photo-frame"></div>
                    <div style="text-align: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.7;"></i>
                    </div>
                    <div style="position: absolute; top: 15px; right: 15px; background: rgba(255,255,255,0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-headphones" style="color: white; font-size: 16px;"></i>
                    </div>
                </div>

                <!-- 主要功能区 -->
                <div style="padding: 0 15px;">
                    <div class="main-actions">
                        <div class="action-card">
                            <div class="icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b35);">
                                <i class="fas fa-camera"></i>
                            </div>
                            <h3>拍摄证件照</h3>
                            <p>自助制作证件照，美颜、换装、冲印</p>
                        </div>
                    </div>

                    <div class="main-actions">
                        <div class="action-card">
                            <div class="icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                <i class="fas fa-user"></i>
                            </div>
                            <h3>我的记录</h3>
                            <p>冲印订单、电子证件照、原始照片</p>
                        </div>
                    </div>

                    <!-- 底部功能区 -->
                    <div style="display: flex; gap: 15px; margin-top: 20px;">
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #fa709a, #fee140); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-female"></i>
                            </div>
                            <h3 style="font-size: 14px;">专业形象照</h3>
                            <p>求职/商务/头像</p>
                        </div>
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #667eea, #764ba2); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-images"></i>
                            </div>
                            <h3 style="font-size: 14px;">证件照采集</h3>
                            <p>学校/企业/社区</p>
                        </div>
                    </div>

                    <!-- 新增图片工具区 -->
                    <div style="display: flex; gap: 15px; margin-top: 15px;">
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #8B0000, #DC143C); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-tools"></i>
                            </div>
                            <h3 style="font-size: 14px;">一键取图</h3>
                            <p>裁剪/压缩/抠图</p>
                        </div>
                        <div class="action-card" style="flex: 1;">
                            <div class="icon" style="background: linear-gradient(135deg, #FF6B35, #F7931E); width: 40px; height: 40px; font-size: 18px;">
                                <i class="fas fa-magic"></i>
                            </div>
                            <h3 style="font-size: 14px;">AI增强</h3>
                            <p>高清/去重/提取</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 规格列表页 - 参考图片风格 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">规格列表</h2>
                </div>
                <div class="header-right">
                    <i class="fas fa-ellipsis-h"></i>
                    <i class="fas fa-question-circle"></i>
                </div>
            </div>
            <div class="screen-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <i class="fas fa-search" style="color: #999;"></i>
                    <input type="text" placeholder="英语,计算机">
                    <button class="search-btn">搜索</button>
                </div>

                <!-- 规格网格 -->
                <div class="spec-grid">
                    <div class="spec-card active">
                        <h3>一寸</h3>
                        <div class="size">25x35mm | 295x413px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>二寸</h3>
                        <div class="size">35x49mm | 413x579px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>原图换底</h3>
                        <div class="size">不改尺寸，只换底色</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card">
                        <h3>导游证</h3>
                        <div class="size">23x33mm | 285x385px</div>
                        <div class="color-dots">
                            <div class="color-dot white"></div>
                        </div>
                    </div>
                </div>

                <!-- 广告区域 -->
                <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); border-radius: 12px; padding: 15px; margin: 20px 0; color: white; position: relative; overflow: hidden;">
                    <div style="position: absolute; right: -20px; top: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <h3 style="margin-bottom: 5px;">足不出户，坐等送货上门</h3>
                    <p style="font-size: 12px; opacity: 0.9;">小象超市</p>
                </div>

                <!-- 更多规格 -->
                <div class="spec-grid">
                    <div class="spec-card">
                        <h3>常规尺寸列表</h3>
                        <div class="size">各种常用尺寸的规格列表</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card hot">
                        <h3>自定义像素</h3>
                        <div class="size">自己设置照片的宽和高</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                            <div class="color-dot navy"></div>
                            <div class="color-dot gray"></div>
                        </div>
                    </div>
                    <div class="spec-card hot">
                        <h3>专业形象照</h3>
                        <div class="size">求职/商务/头像</div>
                    </div>
                    <div class="spec-card">
                        <h3>全国计算机等级考试</h3>
                        <div class="size">33x48mm | 390x567px</div>
                        <div class="color-dots">
                            <div class="color-dot blue"></div>
                            <div class="color-dot white"></div>
                            <div class="color-dot red"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3. 拍照指导页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">拍摄指导</h2>
                </div>
            </div>
            <div class="screen-content" style="background: white; text-align: center;">
                <!-- 示例照片 -->
                <div style="margin: 30px 0;">
                    <div style="width: 180px; height: 240px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                        <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 50px; opacity: 0.8;"></i>
                        </div>
                    </div>
                </div>

                <!-- 拍摄提示 -->
                <div style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin: 20px 0; text-align: left;">
                    <h3 style="margin-bottom: 15px; color: #333;">拍摄要求：</h3>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">正面免冠，表情自然</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">光线充足，背景简洁</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        <span style="font-size: 14px;">着装整洁，符合要求</span>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div style="display: flex; gap: var(--space-lg); margin-top: var(--space-3xl);">
                    <button class="btn btn-secondary btn-full">
                        <i class="fas fa-images"></i>
                        从相册选择
                    </button>
                    <button class="btn btn-primary btn-full">
                        <i class="fas fa-camera"></i>
                        立即拍摄
                    </button>
                </div>
            </div>
        </div>

        <!-- 4. 相机拍摄页 -->
        <div class="screen">
            <div style="background: black; height: 100%; position: relative;">
                <!-- 顶部控制栏 -->
                <div style="position: absolute; top: 0; left: 0; right: 0; z-index: 10; padding: 15px; display: flex; justify-content: space-between; align-items: center;">
                    <button class="back-btn" style="color: white;">
                        <i class="fas fa-times"></i>
                    </button>
                    <div style="color: white; font-size: 14px;">一寸照</div>
                    <button style="background: none; border: none; color: white; font-size: 18px;">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

                <!-- 相机预览区 -->
                <div style="position: absolute; top: 60px; left: 0; right: 0; bottom: 120px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 200px; height: 260px; border: 2px dashed rgba(255,255,255,0.5); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: rgba(255,255,255,0.7);">
                        <div style="text-align: center;">
                            <i class="fas fa-user" style="font-size: 60px; margin-bottom: 10px;"></i>
                            <div style="font-size: 12px;">请将面部对准框内</div>
                        </div>
                    </div>
                </div>

                <!-- 底部控制栏 -->
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 30px; display: flex; justify-content: space-between; align-items: center;">
                    <button style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border: none; border-radius: 50%; color: white; font-size: 20px;">
                        <i class="fas fa-images"></i>
                    </button>
                    <button style="width: 80px; height: 80px; background: white; border: 4px solid rgba(255,255,255,0.3); border-radius: 50%; color: #007AFF; font-size: 30px;">
                        <i class="fas fa-camera"></i>
                    </button>
                    <button style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border: none; border-radius: 50%; color: white; font-size: 20px;">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 5. 证件照编辑页 - 一寸照 (参考图片1) -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">一寸</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #333; font-size: 18px;">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <button style="background: none; border: none; color: #333; font-size: 18px; margin-left: 10px;">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片编辑区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner">
                                <!-- 实际照片内容 -->
                                <div style="width: 100%; height: 100%; background: #007AFF; position: relative; overflow: hidden;">
                                    <!-- 网络头像图片 -->
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=260&fit=crop&crop=face"
                                         style="width: 100%; height: 100%; object-fit: cover; object-position: center top;"
                                         alt="证件照示例">

                                    <!-- 预览图标 -->
                                    <div class="correction-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">295px</div>
                            <div class="photo-dimensions dimension-bottom">25mm</div>
                            <div class="photo-dimensions dimension-left">413px</div>
                            <div class="photo-dimensions dimension-right">35mm</div>
                        </div>
                        <!-- 预览标签 -->
                        <div class="preview-badge">预览图</div>
                        <!-- 正装标识 -->
                        <div style="position: absolute; bottom: 10px; right: 60px; background: rgba(255,255,255,0.9); border-radius: 15px; padding: 5px 10px; font-size: 12px; color: #007AFF;">
                            <i class="fas fa-user-tie" style="margin-right: 5px;"></i>
                            正装
                        </div>
                    </div>


                </div>

                <!-- 换装选择器 -->
                <div class="clothing-selector">
                    <div class="clothing-tabs">
                        <button class="clothing-tab active">女装</button>
                        <button class="clothing-tab">男装</button>
                        <button class="clothing-tab">童装</button>
                        <button style="background: none; border: none; padding: 15px; color: #666;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="clothing-grid">
                        <div class="clothing-item selected">
                            <div class="clothing-placeholder">
                                <i class="fas fa-user" style="font-size: 20px;"></i>
                                <br>无正装
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                lady007
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                lady001
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                lady008
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                lady003
                            </div>
                        </div>
                        <div class="clothing-item">
                            <div class="clothing-placeholder" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                lady010
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 6. 证件照编辑页 - 二寸照 (参考图片2) -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">二寸</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #333; font-size: 18px;">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <button style="background: none; border: none; color: #333; font-size: 18px; margin-left: 10px;">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片编辑区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner" style="width: 220px; height: 300px;">
                                <!-- 实际照片内容 -->
                                <div style="width: 100%; height: 100%; background: #007AFF; position: relative; overflow: hidden;">
                                    <!-- 网络头像图片 -->
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=220&h=300&fit=crop&crop=face"
                                         style="width: 100%; height: 100%; object-fit: cover; object-position: center top;"
                                         alt="证件照示例">

                                    <!-- 预览图标 -->
                                    <div class="correction-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">413px</div>
                            <div class="photo-dimensions dimension-bottom">35mm</div>
                            <div class="photo-dimensions dimension-left">579px</div>
                            <div class="photo-dimensions dimension-right">49mm</div>
                        </div>
                        <!-- 预览标签 -->
                        <div class="preview-badge">预览图</div>
                        <!-- 正装标识 -->
                        <div style="position: absolute; bottom: 10px; right: 60px; background: rgba(255,255,255,0.9); border-radius: 15px; padding: 5px 10px; font-size: 12px; color: #007AFF;">
                            <i class="fas fa-user-tie" style="margin-right: 5px;"></i>
                            正装
                        </div>
                    </div>


                </div>

                <!-- 底部编辑工具 -->
                <div class="edit-tools-bottom">
                    <button class="edit-tool">
                        <i class="fas fa-palette"></i>
                        <span>换底色</span>
                    </button>
                    <button class="edit-tool">
                        <i class="fas fa-font"></i>
                        <span>加文字</span>
                    </button>
                    <button class="edit-tool new">
                        <i class="fas fa-tshirt"></i>
                        <span>换服装</span>
                    </button>
                </div>

                <!-- 底部操作按钮 -->
                <div class="bottom-actions">
                    <button class="action-btn action-btn-outline">
                        <i class="fas fa-download"></i>
                        下载电子照
                    </button>
                    <button class="action-btn action-btn-primary">
                        <i class="fas fa-print"></i>
                        冲印排版照
                    </button>
                </div>
            </div>
        </div>

        <!-- 7. 换底色页面 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">换底色</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text">完成</button>
                </div>
            </div>
            <div class="screen-content" style="background: #f0f0f0; padding: 0;">
                <!-- 照片预览区域 -->
                <div class="photo-editor">
                    <div class="photo-frame-container">
                        <div class="photo-frame-outer">
                            <div class="photo-frame-inner">
                                <div style="width: 100%; height: 100%; background: #007AFF; position: relative; overflow: hidden;">
                                    <!-- 网络头像图片 -->
                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=260&fit=crop&crop=face"
                                         style="width: 100%; height: 100%; object-fit: cover; object-position: center top;"
                                         alt="证件照示例">

                                    <!-- AI处理指示器 -->
                                    <div style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 10px;">
                                        <i class="fas fa-magic" style="margin-right: 4px;"></i>
                                        AI处理中...
                                    </div>
                                </div>
                            </div>
                            <!-- 尺寸标注 -->
                            <div class="photo-dimensions dimension-top">295px</div>
                            <div class="photo-dimensions dimension-bottom">25mm</div>
                            <div class="photo-dimensions dimension-left">413px</div>
                            <div class="photo-dimensions dimension-right">35mm</div>
                        </div>
                    </div>
                </div>

                <!-- 颜色选择区域 -->
                <div style="background: white; padding: 20px;">
                    <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">选择背景色</div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #007AFF; border-radius: 50%; margin: 0 auto 8px; border: 3px solid #007AFF; box-shadow: 0 2px 10px rgba(0,122,255,0.3); position: relative;">
                                <i class="fas fa-check" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 16px;"></i>
                            </div>
                            <div style="font-size: 12px; color: #333; font-weight: 600;">蓝色</div>
                            <div style="font-size: 10px; color: #666;">#007AFF</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: white; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">白色</div>
                            <div style="font-size: 10px; color: #666;">#FFFFFF</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #ff3b30; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">红色</div>
                            <div style="font-size: 10px; color: #666;">#FF3B30</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 60px; height: 60px; background: #1d3557; border-radius: 50%; margin: 0 auto 8px; border: 2px solid #ddd; cursor: pointer;"></div>
                            <div style="font-size: 12px; color: #666;">深蓝</div>
                            <div style="font-size: 10px; color: #666;">#1D3557</div>
                        </div>
                    </div>

                    <!-- 更多颜色 -->
                    <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 10px; margin-bottom: 20px;">
                        <div style="width: 40px; height: 40px; background: #34c759; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #ff9500; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #af52de; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #ff2d92; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: #8e8e93; border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                        <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b35, #f7931e); border-radius: 50%; cursor: pointer; border: 2px solid #ddd;"></div>
                    </div>

                    <!-- 自定义颜色 -->
                    <button style="width: 100%; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 10px; font-size: 14px; margin-bottom: 15px;">
                        <i class="fas fa-palette" style="margin-right: 8px;"></i>
                        自定义颜色
                    </button>

                    <!-- AI智能抠图说明 -->
                    <div style="background: #e3f2fd; border-radius: 10px; padding: 15px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <i class="fas fa-magic" style="color: #007AFF; margin-right: 8px;"></i>
                            <span style="font-weight: 600; color: #333; font-size: 14px;">AI智能抠图</span>
                        </div>
                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                            • 自动识别人像轮廓，精确去除背景<br>
                            • 保留头发丝等细节，抠图更自然<br>
                            • 支持一键更换任意背景颜色
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 8. 美颜编辑页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">美颜调整</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text">完成</button>
                </div>
            </div>
            <div class="screen-content" style="background: white; padding: 0;">
                <!-- 照片预览 -->
                <div style="padding: 20px; text-align: center; background: #f8f9fa;">
                    <div style="width: 200px; height: 260px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.8;"></i>
                    </div>
                </div>

                <!-- 美颜调节项 -->
                <div style="padding: 20px;">
                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">磨皮</span>
                            <span style="font-size: 13px; color: #007AFF;">60</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 60%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 40%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">美白</span>
                            <span style="font-size: 13px; color: #007AFF;">40</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 40%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 60%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">瘦脸</span>
                            <span style="font-size: 13px; color: #007AFF;">30</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 30%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 70%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 15px; color: #333;">大眼</span>
                            <span style="font-size: 13px; color: #007AFF;">20</span>
                        </div>
                        <div style="height: 4px; background: #f0f0f0; border-radius: 2px; position: relative;">
                            <div style="width: 20%; height: 100%; background: #007AFF; border-radius: 2px;"></div>
                            <div style="position: absolute; right: 80%; top: -6px; width: 16px; height: 16px; background: #007AFF; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        </div>
                    </div>

                    <!-- 预设方案 -->
                    <div style="margin-top: 30px;">
                        <div style="font-size: 15px; color: #333; margin-bottom: 15px;">预设方案</div>
                        <div style="display: flex; gap: 10px;">
                            <button style="flex: 1; padding: 10px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 13px;">自然</button>
                            <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 13px;">清新</button>
                            <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 13px;">质感</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 9. 完成预览页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">预览效果</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content" style="background: white; text-align: center;">
                <!-- 最终效果展示 -->
                <div style="padding: 30px 20px;">
                    <div style="width: 200px; height: 260px; margin: 0 auto; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 30px rgba(0,0,0,0.15); background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-user" style="font-size: 60px; opacity: 0.8;"></i>
                    </div>
                    <div style="margin-top: 20px;">
                        <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 5px;">一寸证件照</div>
                        <div style="font-size: 14px; color: #666;">25×35mm | 蓝色背景</div>
                    </div>
                </div>

                <!-- 操作按钮组 -->
                <div style="padding: 0 var(--space-xl);">
                    <button class="btn btn-primary btn-full btn-lg" style="margin-bottom: var(--space-lg);">
                        <i class="fas fa-download"></i>
                        保存到相册
                    </button>

                    <div style="display: flex; gap: var(--space-md); margin-bottom: var(--space-lg);">
                        <button class="btn btn-secondary btn-full">
                            <i class="fas fa-print"></i>
                            冲印
                        </button>
                        <button class="btn btn-secondary btn-full">
                            <i class="fas fa-share"></i>
                            分享
                        </button>
                    </div>

                    <button class="btn btn-outline btn-full">
                        <i class="fas fa-edit"></i>
                        重新编辑
                    </button>
                </div>

                <!-- 规格信息 -->
                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 12px; text-align: left;">
                    <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 10px;">照片信息</div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">尺寸规格</span>
                        <span style="color: #333; font-size: 13px;">25×35mm (一寸)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">像素大小</span>
                        <span style="color: #333; font-size: 13px;">295×413px</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #666; font-size: 13px;">背景颜色</span>
                        <span style="color: #333; font-size: 13px;">蓝色 (#007AFF)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span style="color: #666; font-size: 13px;">文件大小</span>
                        <span style="color: #333; font-size: 13px;">156KB</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 10. 我的记录页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">我的记录</h2>
                </div>
                <div class="header-right">
                    <button style="background: none; border: none; color: #007AFF; font-size: 14px;">管理</button>
                </div>
            </div>
            <div class="screen-content">
                <!-- 统计卡片 -->
                <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #007AFF; margin-bottom: 5px;">12</div>
                        <div style="font-size: 12px; color: #666;">制作次数</div>
                    </div>
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #28a745; margin-bottom: 5px;">8</div>
                        <div style="font-size: 12px; color: #666;">冲印订单</div>
                    </div>
                    <div style="flex: 1; background: white; border-radius: 12px; padding: 15px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: #ff6b35; margin-bottom: 5px;">24</div>
                        <div style="font-size: 12px; color: #666;">保存照片</div>
                    </div>
                </div>

                <!-- 记录列表 -->
                <div style="background: white; border-radius: 12px; overflow: hidden;">
                    <div style="padding: 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">一寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">25×35mm | 蓝色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-15 14:30</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                            <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                            <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                        </div>
                    </div>

                    <div style="padding: 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">二寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">35×49mm | 红色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-12 09:15</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                            <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                            <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                        </div>
                    </div>

                    <div style="padding: 15px; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-size: 15px; font-weight: 600; color: #333; margin-bottom: 3px;">小二寸证件照</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 3px;">35×45mm | 白色背景</div>
                            <div style="font-size: 11px; color: #999;">2024-01-08 16:45</div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                            <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                            <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 11. 图片工具集合页 -->
        <div class="screen">
            <div class="screen-header" style="background: linear-gradient(135deg, #8B0000, #DC143C); color: var(--text-white);">
                <div class="header-left">
                    <button class="back-btn" style="color: var(--text-white);"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; color: var(--text-white); font-size: var(--font-2xl);">一键取图</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text" style="color: var(--text-white); font-size: var(--font-xl);">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <button class="btn-text" style="color: var(--text-white); font-size: var(--font-xl);">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content">

                <!-- 智能图片裁剪 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #2196F3;">
                                <i class="fas fa-crop"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #1976D2;">智能图片裁剪</div>
                                <div class="tool-desc">自动识别主体，裁剪，切图</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #E91E63; color: var(--text-white);">
                            去使用
                        </button>
                    </div>
                </div>

                <!-- 图片压缩 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #9C27B0;">
                                <i class="fas fa-compress-alt"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #7B1FA2;">图片压缩</div>
                                <div class="tool-desc">图片无损压缩，预览压缩</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #7B1FA2; color: var(--text-white);">
                            去使用
                        </button>
                    </div>
                </div>

                <!-- 图片视频提取 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #FF9800;">
                                <i class="fas fa-video"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #F57C00;">图片视频提取</div>
                                <div class="tool-desc">视频提取，动图提取，视频提取</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #E91E63; color: var(--text-white);">
                            去领取
                        </button>
                    </div>
                </div>

                <!-- 图片增强 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #E91E63;">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #C2185B;">图片增强</div>
                                <div class="tool-desc">清晰度提升，色彩优化，无损放大</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #7B1FA2; color: var(--text-white);">
                            去使用
                        </button>
                    </div>
                </div>

                <!-- 高清抠图 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #FF9800;">
                                <i class="fas fa-cut"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #F57C00;">
                                    高清抠图
                                    <span style="background: var(--accent-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-md); margin-left: var(--space-sm);">🔥</span>
                                </div>
                                <div class="tool-desc">电商级抠图，人像抠图，自动抠图</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #FF5722; color: var(--text-white);">
                            试一试
                        </button>
                    </div>
                </div>

                <!-- 图片Md5去重 -->
                <div class="tool-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <div class="tool-icon" style="background: #4CAF50;">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <div>
                                <div class="tool-title" style="color: #388E3C;">图片Md5去重</div>
                                <div class="tool-desc">批量图片去重检测</div>
                            </div>
                        </div>
                        <button class="tool-btn" style="background: #E91E63; color: var(--text-white);">
                            去使用
                        </button>
                    </div>
                </div>

                <!-- 更多AI工具 -->
                <div style="margin-top: var(--space-2xl);">
                    <h3 style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-lg); padding: 0 var(--space-sm);">
                        <i class="fas fa-robot" style="color: var(--primary-color); margin-right: var(--space-sm);"></i>
                        更多AI工具
                    </h3>

                    <!-- 背景替换 -->
                    <div class="tool-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <div class="tool-icon" style="background: #4CAF50;">
                                    <i class="fas fa-image"></i>
                                </div>
                                <div>
                                    <div class="tool-title" style="color: #388E3C;">背景替换</div>
                                    <div class="tool-desc">一键更换证件照背景色</div>
                                </div>
                            </div>
                            <button class="tool-btn" style="background: #4CAF50; color: var(--text-white);">
                                去使用
                            </button>
                        </div>
                    </div>

                    <!-- 人像美化 -->
                    <div class="tool-card" style="background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <div class="tool-icon" style="background: #E91E63;">
                                    <i class="fas fa-user-edit"></i>
                                </div>
                                <div>
                                    <div class="tool-title" style="color: #C2185B;">人像美化</div>
                                    <div class="tool-desc">智能美颜，自然修饰</div>
                                </div>
                            </div>
                            <button class="tool-btn" style="background: #E91E63; color: var(--text-white);">
                                去使用
                            </button>
                        </div>
                    </div>

                    <!-- 格式转换 -->
                    <div class="tool-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <div class="tool-icon" style="background: #2196F3;">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div>
                                    <div class="tool-title" style="color: #1976D2;">格式转换</div>
                                    <div class="tool-desc">JPG/PNG/PDF互转</div>
                                </div>
                            </div>
                            <button class="tool-btn" style="background: #2196F3; color: var(--text-white);">
                                去使用
                            </button>
                        </div>
                    </div>

                    <!-- 批量处理 -->
                    <div class="tool-card" style="background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <div class="tool-icon" style="background: #FF9800;">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div>
                                    <div class="tool-title" style="color: #F57C00;">
                                        批量处理
                                        <span style="background: var(--secondary-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-md); margin-left: var(--space-sm);">NEW</span>
                                    </div>
                                    <div class="tool-desc">同时处理多张图片</div>
                                </div>
                            </div>
                            <button class="tool-btn" style="background: #FF9800; color: var(--text-white);">
                                去使用
                            </button>
                        </div>
                    </div>

                    <!-- 尺寸调整 -->
                    <div class="tool-card" style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center;">
                                <div class="tool-icon" style="background: #9C27B0;">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </div>
                                <div>
                                    <div class="tool-title" style="color: #7B1FA2;">尺寸调整</div>
                                    <div class="tool-desc">精确调整图片尺寸</div>
                                </div>
                            </div>
                            <button class="tool-btn" style="background: #9C27B0; color: var(--text-white);">
                                去使用
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- 12. 智能图片裁剪页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">智能图片裁剪</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text">完成</button>
                </div>
            </div>
            <div class="screen-content" style="background: #f5f5f5; padding: 0;">

                <!-- 图片预览区 -->
                <div style="background: white; padding: 20px; text-align: center;">
                    <div style="position: relative; display: inline-block;">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=400&fit=crop"
                             style="width: 300px; height: 400px; object-fit: cover; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);"
                             alt="待裁剪图片">

                        <!-- 裁剪框 -->
                        <div style="position: absolute; top: 50px; left: 50px; width: 200px; height: 260px; border: 2px dashed #007AFF; background: rgba(0,122,255,0.1);">
                            <!-- 裁剪控制点 -->
                            <div style="position: absolute; top: -5px; left: -5px; width: 10px; height: 10px; background: #007AFF; border-radius: 50%;"></div>
                            <div style="position: absolute; top: -5px; right: -5px; width: 10px; height: 10px; background: #007AFF; border-radius: 50%;"></div>
                            <div style="position: absolute; bottom: -5px; left: -5px; width: 10px; height: 10px; background: #007AFF; border-radius: 50%;"></div>
                            <div style="position: absolute; bottom: -5px; right: -5px; width: 10px; height: 10px; background: #007AFF; border-radius: 50%;"></div>
                        </div>

                        <!-- AI识别标识 -->
                        <div style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                            <i class="fas fa-eye" style="margin-right: 5px;"></i>
                            AI识别中...
                        </div>
                    </div>

                    <div style="margin-top: 15px; font-size: 14px; color: #666;">
                        裁剪尺寸：25×35mm (一寸照)
                    </div>
                </div>

                <!-- 裁剪选项 -->
                <div style="background: white; margin-top: 10px; padding: 20px;">
                    <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">裁剪选项</div>

                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 2px solid #007AFF;">
                            <div style="font-size: 12px; color: #007AFF; font-weight: 600;">一寸照</div>
                            <div style="font-size: 10px; color: #666; margin-top: 5px;">25×35mm</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #ddd; cursor: pointer;">
                            <div style="font-size: 12px; color: #333; font-weight: 600;">二寸照</div>
                            <div style="font-size: 10px; color: #666; margin-top: 5px;">35×49mm</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #ddd; cursor: pointer;">
                            <div style="font-size: 12px; color: #333; font-weight: 600;">自定义</div>
                            <div style="font-size: 10px; color: #666; margin-top: 5px;">任意尺寸</div>
                        </div>
                    </div>

                    <!-- 智能功能 -->
                    <div style="background: #e3f2fd; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <i class="fas fa-brain" style="color: #007AFF; margin-right: 10px; font-size: 16px;"></i>
                            <span style="font-weight: 600; color: #333;">AI智能识别</span>
                        </div>
                        <div style="font-size: 13px; color: #666; line-height: 1.4; margin-bottom: 10px;">
                            • 自动识别人脸位置，智能调整裁剪区域<br>
                            • 保证人像居中，符合证件照标准<br>
                            • 支持批量处理，提高工作效率
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button style="flex: 1; padding: 8px; background: #007AFF; color: white; border: none; border-radius: 6px; font-size: 12px;">
                                <i class="fas fa-magic" style="margin-right: 5px;"></i>
                                智能裁剪
                            </button>
                            <button style="flex: 1; padding: 8px; background: white; color: #007AFF; border: 1px solid #007AFF; border-radius: 6px; font-size: 12px;">
                                <i class="fas fa-hand-paper" style="margin-right: 5px;"></i>
                                手动调整
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 底部操作按钮 -->
                <div style="padding: var(--space-xl); background: var(--bg-primary); margin-top: var(--space-md);">
                    <div style="display: flex; gap: var(--space-lg);">
                        <button class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                        <button class="btn btn-primary" style="flex: 2;">
                            <i class="fas fa-crop"></i>
                            确认裁剪
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 13. 用户登录页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; font-size: var(--font-2xl);">登录</h2>
                </div>
            </div>
            <div class="screen-content">

                <!-- Logo区域 -->
                <div style="text-align: center; padding: var(--space-3xl) 0;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); border-radius: var(--radius-xl); margin: 0 auto var(--space-xl); display: flex; align-items: center; justify-content: center; box-shadow: var(--shadow-lg);">
                        <i class="fas fa-camera" style="font-size: var(--font-4xl); color: var(--text-white);"></i>
                    </div>
                    <h1 style="font-size: var(--font-3xl); font-weight: var(--font-bold); color: var(--text-primary); margin-bottom: var(--space-sm);">证件照大师</h1>
                    <p style="color: var(--text-secondary); font-size: var(--font-md);">专业证件照制作工具</p>
                </div>

                <!-- 登录表单 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-body">
                        <div style="margin-bottom: var(--space-xl);">
                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">手机号</label>
                            <input type="tel" class="form-input" placeholder="请输入手机号" style="margin-bottom: var(--space-lg);">

                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">验证码</label>
                            <div style="display: flex; gap: var(--space-md);">
                                <input type="text" class="form-input" placeholder="请输入验证码" style="flex: 1;">
                                <button class="btn btn-outline" style="white-space: nowrap;">获取验证码</button>
                            </div>
                        </div>

                        <button class="btn btn-primary btn-full btn-lg">
                            <i class="fas fa-sign-in-alt"></i>
                            登录
                        </button>

                        <div style="text-align: center; margin-top: var(--space-xl);">
                            <span style="color: var(--text-secondary); font-size: var(--font-sm);">还没有账号？</span>
                            <button class="btn-text" style="font-size: var(--font-sm);">立即注册</button>
                        </div>
                    </div>
                </div>

                <!-- 第三方登录 -->
                <div class="card">
                    <div class="card-body">
                        <div style="text-align: center; margin-bottom: var(--space-lg);">
                            <span style="color: var(--text-secondary); font-size: var(--font-sm);">其他登录方式</span>
                        </div>

                        <div style="display: flex; justify-content: center; gap: var(--space-2xl);">
                            <button style="width: 50px; height: 50px; border-radius: var(--radius-full); background: #1AAD19; border: none; display: flex; align-items: center; justify-content: center; box-shadow: var(--shadow-sm);">
                                <i class="fab fa-weixin" style="font-size: var(--font-xl); color: var(--text-white);"></i>
                            </button>
                            <button style="width: 50px; height: 50px; border-radius: var(--radius-full); background: #FF4500; border: none; display: flex; align-items: center; justify-content: center; box-shadow: var(--shadow-sm);">
                                <i class="fab fa-weibo" style="font-size: var(--font-xl); color: var(--text-white);"></i>
                            </button>
                            <button style="width: 50px; height: 50px; border-radius: var(--radius-full); background: #1677FF; border: none; display: flex; align-items: center; justify-content: center; box-shadow: var(--shadow-sm);">
                                <i class="fab fa-qq" style="font-size: var(--font-xl); color: var(--text-white);"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 协议条款 -->
                <div style="text-align: center; margin-top: var(--space-xl); padding: 0 var(--space-lg);">
                    <p style="font-size: var(--font-xs); color: var(--text-tertiary); line-height: 1.5;">
                        登录即表示同意
                        <button class="btn-text" style="font-size: var(--font-xs);">《用户协议》</button>
                        和
                        <button class="btn-text" style="font-size: var(--font-xs);">《隐私政策》</button>
                    </p>
                </div>
            </div>
        </div>

        <!-- 14. 用户注册页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; font-size: var(--font-2xl);">注册</h2>
                </div>
            </div>
            <div class="screen-content">

                <!-- 注册表单 -->
                <div class="card" style="margin-top: var(--space-2xl);">
                    <div class="card-header">
                        <h3 style="margin: 0; font-size: var(--font-lg);">创建新账号</h3>
                    </div>
                    <div class="card-body">
                        <div style="margin-bottom: var(--space-xl);">
                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">手机号</label>
                            <input type="tel" class="form-input" placeholder="请输入手机号" style="margin-bottom: var(--space-lg);">

                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">验证码</label>
                            <div style="display: flex; gap: var(--space-md); margin-bottom: var(--space-lg);">
                                <input type="text" class="form-input" placeholder="请输入验证码" style="flex: 1;">
                                <button class="btn btn-outline" style="white-space: nowrap;">获取验证码</button>
                            </div>

                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">昵称</label>
                            <input type="text" class="form-input" placeholder="请输入昵称" style="margin-bottom: var(--space-lg);">

                            <label style="display: block; margin-bottom: var(--space-sm); font-weight: var(--font-medium); color: var(--text-primary);">密码</label>
                            <input type="password" class="form-input" placeholder="请设置密码（6-20位）">
                        </div>

                        <!-- 协议勾选 -->
                        <div style="display: flex; align-items: center; margin-bottom: var(--space-xl);">
                            <input type="checkbox" id="agree" style="margin-right: var(--space-sm);">
                            <label for="agree" style="font-size: var(--font-sm); color: var(--text-secondary);">
                                我已阅读并同意
                                <button class="btn-text" style="font-size: var(--font-sm);">《用户协议》</button>
                                和
                                <button class="btn-text" style="font-size: var(--font-sm);">《隐私政策》</button>
                            </label>
                        </div>

                        <button class="btn btn-primary btn-full btn-lg">
                            <i class="fas fa-user-plus"></i>
                            立即注册
                        </button>

                        <div style="text-align: center; margin-top: var(--space-xl);">
                            <span style="color: var(--text-secondary); font-size: var(--font-sm);">已有账号？</span>
                            <button class="btn-text" style="font-size: var(--font-sm);">立即登录</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 15. 个人中心页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <h2 style="margin: 0; font-size: var(--font-2xl);">个人中心</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text"><i class="fas fa-cog"></i></button>
                </div>
            </div>
            <div class="screen-content">

                <!-- 用户信息卡片 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-body">
                        <div style="display: flex; align-items: center; gap: var(--space-lg);">
                            <div style="width: 60px; height: 60px; border-radius: var(--radius-full); background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); display: flex; align-items: center; justify-content: center; position: relative;">
                                <i class="fas fa-user" style="font-size: var(--font-xl); color: var(--text-white);"></i>
                                <!-- VIP标识 -->
                                <div style="position: absolute; bottom: -2px; right: -2px; width: 20px; height: 20px; background: linear-gradient(135deg, #FFD700, #FFA500); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center; border: 2px solid var(--bg-primary);">
                                    <i class="fas fa-crown" style="font-size: 8px; color: var(--text-white);"></i>
                                </div>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-xs);">
                                    张小明
                                    <span style="background: linear-gradient(135deg, #FFD700, #FFA500); color: var(--text-white); font-size: var(--font-xs); padding: 2px 6px; border-radius: var(--radius-sm); margin-left: var(--space-sm);">VIP</span>
                                </div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">138****8888</div>
                                <div style="font-size: var(--font-xs); color: var(--text-tertiary);">VIP会员 · 到期时间：2024-12-31</div>
                            </div>
                            <button class="btn btn-outline btn-sm">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--space-md); margin-bottom: var(--space-xl);">
                    <div class="card">
                        <div class="card-body" style="text-align: center; padding: var(--space-lg);">
                            <div style="font-size: var(--font-2xl); font-weight: var(--font-bold); color: var(--primary-color); margin-bottom: var(--space-xs);">156</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">制作次数</div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body" style="text-align: center; padding: var(--space-lg);">
                            <div style="font-size: var(--font-2xl); font-weight: var(--font-bold); color: var(--secondary-color); margin-bottom: var(--space-xs);">23</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">冲印订单</div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body" style="text-align: center; padding: var(--space-lg);">
                            <div style="font-size: var(--font-2xl); font-weight: var(--font-bold); color: var(--warning-color); margin-bottom: var(--space-xs);">89</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">云端存储</div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-body" style="padding: 0;">
                        <!-- 我的订单 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); display: flex; align-items: center; cursor: pointer;" class="menu-item">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #4facfe, #00f2fe); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; margin-right: var(--space-lg);">
                                <i class="fas fa-shopping-bag" style="color: var(--text-white); font-size: var(--font-lg);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">我的订单</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">查看冲印订单和购买记录</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                        </div>

                        <!-- 云端存储 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); display: flex; align-items: center; cursor: pointer;" class="menu-item">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; margin-right: var(--space-lg);">
                                <i class="fas fa-cloud" style="color: var(--text-white); font-size: var(--font-lg);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">云端存储</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">已使用 2.3GB / 10GB</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                        </div>

                        <!-- 会员中心 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); display: flex; align-items: center; cursor: pointer;" class="menu-item">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #FFD700, #FFA500); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; margin-right: var(--space-lg);">
                                <i class="fas fa-crown" style="color: var(--text-white); font-size: var(--font-lg);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">会员中心</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">享受更多专属权益</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                        </div>

                        <!-- 帮助中心 -->
                        <div style="padding: var(--space-lg) var(--space-xl); display: flex; align-items: center; cursor: pointer;" class="menu-item">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #fa709a, #fee140); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; margin-right: var(--space-lg);">
                                <i class="fas fa-question-circle" style="color: var(--text-white); font-size: var(--font-lg);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">帮助中心</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">常见问题和使用指南</div>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                        </div>
                    </div>
                </div>

                <!-- 退出登录 -->
                <button class="btn btn-outline btn-full" style="color: var(--accent-color); border-color: var(--accent-color);">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </button>
            </div>
        </div>

        <!-- 16. 会员中心页 -->
        <div class="screen">
            <div class="screen-header" style="background: linear-gradient(135deg, #FFD700, #FFA500); color: var(--text-white);">
                <div class="header-left">
                    <button class="back-btn" style="color: var(--text-white);"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; color: var(--text-white); font-size: var(--font-2xl);">会员中心</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text" style="color: var(--text-white);">
                        <i class="fas fa-gift"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content">

                <!-- VIP状态卡片 -->
                <div style="background: linear-gradient(135deg, #FFD700, #FFA500); border-radius: var(--radius-lg); padding: var(--space-2xl); margin-bottom: var(--space-xl); color: var(--text-white); position: relative; overflow: hidden;">
                    <!-- 装饰背景 -->
                    <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: var(--radius-full);"></div>
                    <div style="position: absolute; bottom: -30px; left: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: var(--radius-full);"></div>

                    <div style="position: relative; z-index: 1;">
                        <div style="display: flex; align-items: center; margin-bottom: var(--space-lg);">
                            <i class="fas fa-crown" style="font-size: var(--font-2xl); margin-right: var(--space-md);"></i>
                            <div>
                                <div style="font-size: var(--font-xl); font-weight: var(--font-bold);">VIP会员</div>
                                <div style="font-size: var(--font-sm); opacity: 0.9;">尊享专属特权</div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-size: var(--font-sm); opacity: 0.9; margin-bottom: var(--space-xs);">到期时间</div>
                                <div style="font-size: var(--font-lg); font-weight: var(--font-semibold);">2024-12-31</div>
                            </div>
                            <button class="btn" style="background: var(--text-white); color: #FFA500; font-weight: var(--font-semibold);">
                                续费
                            </button>
                        </div>
                    </div>
                </div>

                <!-- VIP特权 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-header">
                        <h3 style="margin: 0; font-size: var(--font-lg);">
                            <i class="fas fa-star" style="color: #FFD700; margin-right: var(--space-sm);"></i>
                            VIP专属特权
                        </h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1px; background: var(--border-light);">
                            <div style="background: var(--bg-primary); padding: var(--space-lg); text-align: center;">
                                <i class="fas fa-infinity" style="font-size: var(--font-2xl); color: var(--primary-color); margin-bottom: var(--space-sm);"></i>
                                <div style="font-size: var(--font-sm); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">无限制作</div>
                                <div style="font-size: var(--font-xs); color: var(--text-secondary);">不限次数制作证件照</div>
                            </div>
                            <div style="background: var(--bg-primary); padding: var(--space-lg); text-align: center;">
                                <i class="fas fa-cloud-upload-alt" style="font-size: var(--font-2xl); color: var(--secondary-color); margin-bottom: var(--space-sm);"></i>
                                <div style="font-size: var(--font-sm); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">云端存储</div>
                                <div style="font-size: var(--font-xs); color: var(--text-secondary);">10GB专属云空间</div>
                            </div>
                            <div style="background: var(--bg-primary); padding: var(--space-lg); text-align: center;">
                                <i class="fas fa-magic" style="font-size: var(--font-2xl); color: var(--warning-color); margin-bottom: var(--space-sm);"></i>
                                <div style="font-size: var(--font-sm); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">AI增强</div>
                                <div style="font-size: var(--font-xs); color: var(--text-secondary);">专业AI美化功能</div>
                            </div>
                            <div style="background: var(--bg-primary); padding: var(--space-lg); text-align: center;">
                                <i class="fas fa-shipping-fast" style="font-size: var(--font-2xl); color: var(--accent-color); margin-bottom: var(--space-sm);"></i>
                                <div style="font-size: var(--font-sm); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">优先处理</div>
                                <div style="font-size: var(--font-xs); color: var(--text-secondary);">冲印订单优先制作</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 套餐选择 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-header">
                        <h3 style="margin: 0; font-size: var(--font-lg);">选择套餐</h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <!-- 月度套餐 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-size: var(--font-md); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-xs);">月度会员</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">30天VIP特权</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-bold); color: var(--primary-color);">¥19</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary); text-decoration: line-through;">¥29</div>
                                </div>
                            </div>
                        </div>

                        <!-- 年度套餐 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); cursor: pointer; position: relative;" class="menu-item">
                            <!-- 推荐标签 -->
                            <div style="position: absolute; top: -1px; right: var(--space-xl); background: var(--accent-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: 0 0 var(--space-sm) var(--space-sm);">推荐</div>

                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-size: var(--font-md); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-xs);">年度会员</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">365天VIP特权，平均每月仅需¥9.9</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-bold); color: var(--primary-color);">¥119</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary); text-decoration: line-through;">¥348</div>
                                </div>
                            </div>
                        </div>

                        <!-- 终身套餐 -->
                        <div style="padding: var(--space-lg) var(--space-xl); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-size: var(--font-md); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-xs);">终身会员</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">一次购买，终身享受</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-bold); color: var(--primary-color);">¥299</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary); text-decoration: line-through;">¥999</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 立即开通按钮 -->
                <button class="btn btn-primary btn-full btn-lg" style="background: linear-gradient(135deg, #FFD700, #FFA500); margin-bottom: var(--space-lg);">
                    <i class="fas fa-crown"></i>
                    立即开通VIP
                </button>

                <!-- 说明文字 -->
                <div style="text-align: center; color: var(--text-tertiary); font-size: var(--font-xs); line-height: 1.5;">
                    <p>• 开通即可享受所有VIP特权</p>
                    <p>• 支持微信、支付宝、银行卡支付</p>
                    <p>• 7天无理由退款保障</p>
                </div>
            </div>
        </div>

        <!-- 17. 订单管理页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; font-size: var(--font-2xl);">我的订单</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text"><i class="fas fa-search"></i></button>
                </div>
            </div>
            <div class="screen-content">

                <!-- 订单状态筛选 -->
                <div style="display: flex; gap: var(--space-sm); margin-bottom: var(--space-xl); overflow-x: auto; padding-bottom: var(--space-sm);">
                    <button class="btn btn-primary btn-sm" style="white-space: nowrap;">全部</button>
                    <button class="btn btn-secondary btn-sm" style="white-space: nowrap;">待支付</button>
                    <button class="btn btn-secondary btn-sm" style="white-space: nowrap;">制作中</button>
                    <button class="btn btn-secondary btn-sm" style="white-space: nowrap;">待发货</button>
                    <button class="btn btn-secondary btn-sm" style="white-space: nowrap;">已完成</button>
                </div>

                <!-- 订单列表 -->
                <div style="display: flex; flex-direction: column; gap: var(--space-lg);">

                    <!-- 订单1 - 制作中 -->
                    <div class="card">
                        <div class="card-body">
                            <!-- 订单头部 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-lg); padding-bottom: var(--space-md); border-bottom: 1px solid var(--border-light);">
                                <div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">订单号：202401150001</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-15 14:30</div>
                                </div>
                                <span style="background: var(--warning-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-sm);">制作中</span>
                            </div>

                            <!-- 订单内容 -->
                            <div style="display: flex; gap: var(--space-lg); margin-bottom: var(--space-lg);">
                                <div style="width: 60px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">一寸证件照冲印</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">25×35mm | 蓝色背景 | 8张</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">收货地址：北京市朝阳区xxx街道</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--primary-color);">¥12.8</div>
                                </div>
                            </div>

                            <!-- 订单操作 -->
                            <div style="display: flex; justify-content: flex-end; gap: var(--space-md);">
                                <button class="btn btn-secondary btn-sm">联系客服</button>
                                <button class="btn btn-primary btn-sm">查看详情</button>
                            </div>
                        </div>
                    </div>

                    <!-- 订单2 - 已完成 -->
                    <div class="card">
                        <div class="card-body">
                            <!-- 订单头部 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-lg); padding-bottom: var(--space-md); border-bottom: 1px solid var(--border-light);">
                                <div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">订单号：202401120002</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-12 09:15</div>
                                </div>
                                <span style="background: var(--secondary-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-sm);">已完成</span>
                            </div>

                            <!-- 订单内容 -->
                            <div style="display: flex; gap: var(--space-lg); margin-bottom: var(--space-lg);">
                                <div style="width: 60px; height: 80px; background: linear-gradient(135deg, #ff6b35, #f7931e); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">二寸证件照冲印</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">35×49mm | 红色背景 | 6张</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">已送达：北京市朝阳区xxx街道</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--primary-color);">¥15.6</div>
                                </div>
                            </div>

                            <!-- 订单操作 -->
                            <div style="display: flex; justify-content: flex-end; gap: var(--space-md);">
                                <button class="btn btn-secondary btn-sm">再次购买</button>
                                <button class="btn btn-outline btn-sm">评价</button>
                            </div>
                        </div>
                    </div>

                    <!-- 订单3 - 待支付 -->
                    <div class="card">
                        <div class="card-body">
                            <!-- 订单头部 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-lg); padding-bottom: var(--space-md); border-bottom: 1px solid var(--border-light);">
                                <div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary);">订单号：202401100003</div>
                                    <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-10 16:45</div>
                                </div>
                                <span style="background: var(--accent-color); color: var(--text-white); font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-sm);">待支付</span>
                            </div>

                            <!-- 订单内容 -->
                            <div style="display: flex; gap: var(--space-lg); margin-bottom: var(--space-lg);">
                                <div style="width: 60px; height: 80px; background: linear-gradient(135deg, #28a745, #20c997); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">小二寸证件照冲印</div>
                                    <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">35×45mm | 白色背景 | 10张</div>
                                    <div style="font-size: var(--font-sm); color: var(--accent-color);">剩余支付时间：23分58秒</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--primary-color);">¥18.8</div>
                                </div>
                            </div>

                            <!-- 订单操作 -->
                            <div style="display: flex; justify-content: flex-end; gap: var(--space-md);">
                                <button class="btn btn-secondary btn-sm">取消订单</button>
                                <button class="btn btn-primary btn-sm">立即支付</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态提示 -->
                <div style="text-align: center; padding: var(--space-3xl) 0; color: var(--text-tertiary); display: none;" id="empty-state">
                    <i class="fas fa-shopping-bag" style="font-size: var(--font-4xl); margin-bottom: var(--space-lg); opacity: 0.3;"></i>
                    <div style="font-size: var(--font-md); margin-bottom: var(--space-sm);">暂无订单</div>
                    <div style="font-size: var(--font-sm);">快去制作你的第一张证件照吧</div>
                </div>
            </div>
        </div>

        <!-- 18. 帮助中心页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; font-size: var(--font-2xl);">帮助中心</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text"><i class="fas fa-headset"></i></button>
                </div>
            </div>
            <div class="screen-content">

                <!-- 搜索框 -->
                <div class="search-bar" style="margin-bottom: var(--space-2xl);">
                    <i class="fas fa-search" style="color: var(--text-tertiary);"></i>
                    <input type="text" placeholder="搜索问题或关键词">
                </div>

                <!-- 热门问题 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-header">
                        <h3 style="margin: 0; font-size: var(--font-lg);">
                            <i class="fas fa-fire" style="color: var(--accent-color); margin-right: var(--space-sm);"></i>
                            热门问题
                        </h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: var(--font-md); color: var(--text-primary);">如何制作标准的一寸证件照？</div>
                                <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                            </div>
                        </div>
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: var(--font-md); color: var(--text-primary);">证件照背景颜色有什么要求？</div>
                                <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                            </div>
                        </div>
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: var(--font-md); color: var(--text-primary);">冲印的照片多久能收到？</div>
                                <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                            </div>
                        </div>
                        <div style="padding: var(--space-lg) var(--space-xl); cursor: pointer;" class="menu-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: var(--font-md); color: var(--text-primary);">如何申请退款？</div>
                                <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能分类 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-lg); margin-bottom: var(--space-xl);">
                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-camera" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">拍摄指南</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">拍摄技巧和要求</div>
                        </div>
                    </div>

                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--secondary-color), #20c997); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-print" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">冲印服务</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">冲印流程和配送</div>
                        </div>
                    </div>

                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--warning-color), #FFA500); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-crown" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">会员服务</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">VIP权益说明</div>
                        </div>
                    </div>

                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--accent-color), #dc3545); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-shield-alt" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">账号安全</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">隐私和安全设置</div>
                        </div>
                    </div>
                </div>

                <!-- 联系客服 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-body">
                        <div style="text-align: center; margin-bottom: var(--space-lg);">
                            <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #4facfe, #00f2fe); border-radius: var(--radius-full); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-headset" style="color: var(--text-white); font-size: var(--font-2xl);"></i>
                            </div>
                            <h3 style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-sm);">还有问题？</h3>
                            <p style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-lg);">我们的客服团队随时为您提供帮助</p>
                        </div>

                        <div style="display: flex; gap: var(--space-md);">
                            <button class="btn btn-outline btn-full">
                                <i class="fas fa-comments"></i>
                                在线客服
                            </button>
                            <button class="btn btn-primary btn-full">
                                <i class="fas fa-phone"></i>
                                电话咨询
                            </button>
                        </div>

                        <div style="text-align: center; margin-top: var(--space-lg); padding-top: var(--space-lg); border-top: 1px solid var(--border-light);">
                            <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">客服热线：************</div>
                            <div style="font-size: var(--font-xs); color: var(--text-tertiary);">服务时间：周一至周日 9:00-21:00</div>
                        </div>
                    </div>
                </div>

                <!-- 意见反馈 -->
                <button class="btn btn-secondary btn-full">
                    <i class="fas fa-comment-alt"></i>
                    意见反馈
                </button>
            </div>
        </div>

        <!-- 19. 云端存储页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0; font-size: var(--font-2xl);">云端存储</h2>
                </div>
                <div class="header-right">
                    <button class="btn-text"><i class="fas fa-upload"></i></button>
                    <button class="btn-text"><i class="fas fa-ellipsis-h"></i></button>
                </div>
            </div>
            <div class="screen-content">

                <!-- 存储空间统计 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-body">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                            <div>
                                <h3 style="font-size: var(--font-lg); font-weight: var(--font-semibold); color: var(--text-primary); margin-bottom: var(--space-xs);">存储空间</h3>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">已使用 2.3GB / 10GB</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: var(--font-2xl); font-weight: var(--font-bold); color: var(--primary-color);">23%</div>
                                <div style="font-size: var(--font-xs); color: var(--text-tertiary);">使用率</div>
                            </div>
                        </div>

                        <!-- 进度条 -->
                        <div style="background: var(--bg-secondary); height: 8px; border-radius: var(--radius-sm); overflow: hidden; margin-bottom: var(--space-lg);">
                            <div style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); width: 23%; height: 100%; border-radius: var(--radius-sm);"></div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">剩余 7.7GB 可用</div>
                            <button class="btn btn-outline btn-sm">扩容</button>
                        </div>
                    </div>
                </div>

                <!-- 文件分类 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-md); margin-bottom: var(--space-xl);">
                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center; padding: var(--space-lg);">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">证件照</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">156张 · 1.8GB</div>
                        </div>
                    </div>

                    <div class="card" style="cursor: pointer;">
                        <div class="card-body" style="text-align: center; padding: var(--space-lg);">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #4facfe, #00f2fe); border-radius: var(--radius-md); margin: 0 auto var(--space-md); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-images" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">原始照片</div>
                            <div style="font-size: var(--font-sm); color: var(--text-secondary);">89张 · 0.5GB</div>
                        </div>
                    </div>
                </div>

                <!-- 最近文件 -->
                <div class="card" style="margin-bottom: var(--space-xl);">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h3 style="margin: 0; font-size: var(--font-lg);">最近文件</h3>
                            <button class="btn-text btn-sm">查看全部</button>
                        </div>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <!-- 文件项1 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); display: flex; align-items: center; gap: var(--space-lg);">
                            <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">一寸证件照_蓝底.jpg</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">295×413px · 156KB</div>
                                <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-15 14:30</div>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                                <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                                <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                            </div>
                        </div>

                        <!-- 文件项2 -->
                        <div style="padding: var(--space-lg) var(--space-xl); border-bottom: 1px solid var(--border-light); display: flex; align-items: center; gap: var(--space-lg);">
                            <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #ff6b35, #f7931e); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">二寸证件照_红底.jpg</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">413×579px · 234KB</div>
                                <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-12 09:15</div>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                                <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                                <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                            </div>
                        </div>

                        <!-- 文件项3 -->
                        <div style="padding: var(--space-lg) var(--space-xl); display: flex; align-items: center; gap: var(--space-lg);">
                            <div style="width: 50px; height: 65px; background: linear-gradient(135deg, #28a745, #20c997); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="color: var(--text-white); font-size: var(--font-xl);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">小二寸证件照_白底.jpg</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary); margin-bottom: var(--space-xs);">390×567px · 198KB</div>
                                <div style="font-size: var(--font-xs); color: var(--text-tertiary);">2024-01-08 16:45</div>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: var(--space-xs);">
                                <button class="btn btn-primary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">下载</button>
                                <button class="btn btn-secondary btn-sm" style="font-size: var(--font-xs); padding: var(--space-xs) var(--space-sm);">分享</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 同步状态 -->
                <div class="card">
                    <div class="card-body">
                        <div style="display: flex; align-items: center; gap: var(--space-lg);">
                            <div style="width: 40px; height: 40px; background: var(--secondary-color); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-sync-alt" style="color: var(--text-white); font-size: var(--font-lg);"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: var(--font-md); font-weight: var(--font-medium); color: var(--text-primary); margin-bottom: var(--space-xs);">自动同步已开启</div>
                                <div style="font-size: var(--font-sm); color: var(--text-secondary);">您的文件将自动备份到云端</div>
                            </div>
                            <button class="btn btn-outline btn-sm">设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 20. 专业形象照页 -->
        <div class="screen">
            <div class="screen-header">
                <div class="header-left">
                    <button class="back-btn"><i class="fas fa-arrow-left"></i></button>
                    <h2 style="margin: 0;">专业形象照</h2>
                </div>
            </div>
            <div class="screen-content">
                <!-- 类型选择 -->
                <div style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button style="flex: 1; padding: 10px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 14px;">求职照</button>
                        <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">商务照</button>
                        <button style="flex: 1; padding: 10px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">头像照</button>
                    </div>
                </div>

                <!-- 模板展示 -->
                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div style="width: 80px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user-tie" style="font-size: 30px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin-bottom: 8px; font-size: 16px;">商务正装</h3>
                            <p style="font-size: 13px; color: #666; line-height: 1.4; margin-bottom: 10px;">适合求职面试、职场商务等正式场合使用</p>
                            <div style="display: flex; gap: 5px;">
                                <span style="background: #e3f2fd; color: #007AFF; padding: 2px 8px; border-radius: 12px; font-size: 11px;">正装</span>
                                <span style="background: #e8f5e8; color: #28a745; padding: 2px 8px; border-radius: 12px; font-size: 11px;">专业</span>
                            </div>
                        </div>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #007AFF; color: white; border: none; border-radius: 8px; font-size: 14px;">
                        选择此模板
                    </button>
                </div>

                <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div style="width: 80px; height: 100px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user" style="font-size: 30px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin-bottom: 8px; font-size: 16px;">休闲商务</h3>
                            <p style="font-size: 13px; color: #666; line-height: 1.4; margin-bottom: 10px;">适合创意行业、互联网公司等轻松环境</p>
                            <div style="display: flex; gap: 5px;">
                                <span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 11px;">休闲</span>
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 11px;">时尚</span>
                            </div>
                        </div>
                    </div>
                    <button style="width: 100%; padding: 12px; background: #f8f9fa; color: #333; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                        选择此模板
                    </button>
                </div>

                <!-- 拍摄提示 -->
                <div style="background: #e3f2fd; border-radius: 10px; padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <i class="fas fa-lightbulb" style="color: #007AFF; margin-right: 10px;"></i>
                        <span style="font-weight: 600; color: #333;">拍摄建议</span>
                    </div>
                    <ul style="font-size: 13px; color: #666; line-height: 1.5; margin-left: 20px;">
                        <li>选择合适的服装搭配</li>
                        <li>保持自然的表情和姿态</li>
                        <li>注意光线和背景环境</li>
                        <li>可适当进行美颜处理</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
